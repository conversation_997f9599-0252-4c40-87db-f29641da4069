server:
  port: 8080

spring:
  main:
    allow-circular-references: true
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  data:
    redis:
      host: hip-gscm-redis-dev.rds-a2-np.kob.dell.com
      port: 443
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false
  config:
    import: "optional:configserver:"

service:
  manager:
    name: TestIntegrationManager
  concurrency:
    corePoolSize: 2
    maxPoolSize: 4
    queueCapacity: 100

hip:
  kafka:
    bootstrap-servers: "localhost:9092"
    security-protocol: PLAINTEXT

# Logging and monitoring configs...
logging:
  level:
    root: info
    com.dell.it.hip: debug
